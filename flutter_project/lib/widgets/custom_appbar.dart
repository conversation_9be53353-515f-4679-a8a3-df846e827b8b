import 'package:flutter/material.dart';
import 'package:flutter_project/core/app_export.dart';
import 'package:flutter_project/core/utils/app_dimensions.dart';
import 'package:flutter_project/presentation/main_navigation/bloc/drawer_bloc.dart';
import 'package:flutter_project/widgets/custom_image_view.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      surfaceTintColor: Colors.transparent,
      shadowColor: Theme.of(context).colorScheme.shadow,
      backgroundColor: Theme.of(context).colorScheme.surface,
      centerTitle: false, // Material 2 usually doesn't center titles by default
      iconTheme: IconThemeData(color: Theme.of(context).colorScheme.onSurface),
      leading: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.appBarLeadingPadding),
        child: CustomImageView(
          imagePath: ImageConstant.imgLogomark,
          height: AppDimensions.appBarLeadingIconSize.h,
          width: AppDimensions.appBarLeadingIconSize.h,
        ),
      ),
      leadingWidth: AppDimensions.appBarLeadingWidth + AppDimensions.appBarLeadingExtraPadding,
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: AppDimensions.appBarActionPadding),
          child: IconButton(
            onPressed: () {
              // Try to find DrawerBloc and open drawer
              try {
                context.read<DrawerBloc>().add(ToggleDrawerEvent());
                Scaffold.of(context).openEndDrawer();
              } catch (e) {
                // Log the error for debugging
                debugPrint('Failed to open drawer: $e');

                // Provide user feedback
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Unable to open menu'),
                    duration: Duration(seconds: AppDimensions.snackBarDurationSeconds),
                  ),
                );
              }
            },
            icon: CustomImageView(
              imagePath: ImageConstant.imgMenu,
              height: AppDimensions.appBarMenuIconSize.h,
              width: AppDimensions.appBarMenuIconSize.h,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ],
    );
  }
}
