import 'package:flutter/material.dart';

import '../../core/app_export.dart';
import '../../core/di/injection_container.dart' as di;
import '../../widgets/custom_button.dart';
import '../../widgets/custom_image_view.dart';
import '../../widgets/custom_input_field.dart';
import 'bloc/authentication_bloc.dart';

class AuthenticationScreen extends StatelessWidget {
  const AuthenticationScreen({super.key});

  static Widget builder(BuildContext context) {
    return BlocProvider<AuthenticationBloc>(
      create: (context) => di.sl<AuthenticationBloc>()..add(AuthenticationInitialEvent()),
      child: const AuthenticationScreen(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: appTheme.whiteCustom,
      body: BlocConsumer<AuthenticationBloc, AuthenticationState>(
        listener: (context, state) {
          if (state.isLoginSuccess ?? false) {
            NavigatorService.pushNamedAndRemoveUntil(AppRoutes.mainNavigationScreen);
          }
          if (state.showError ?? false) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.errorMessage ?? 'Login failed'), backgroundColor: appTheme.redCustom, behavior: SnackBarBehavior.floating),
            );
          }
        },
        builder: (context, state) {
          // Debug print to see state changes
          print('DEBUG: emailError = ${state.emailError}, passwordError = ${state.passwordError}, isLoading = ${state.isLoading}');
          return SizedBox(
            width: double.maxFinite,
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              child: SafeArea(
                child: Container(
                  constraints: BoxConstraints(maxWidth: 400.h),
                  margin: EdgeInsets.symmetric(horizontal: 16.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: 44.h),
                      _buildIllustrationSection(),
                      SizedBox(height: 32.h),
                      _buildHeaderSection(),
                      SizedBox(height: 32.h),
                      _buildLoginForm(context, state),
                      SizedBox(height: 32.h),
                      _buildSocialLoginSection(context),
                      SizedBox(height: 32.h),
                      _buildSignUpSection(context),
                      SizedBox(height: 32.h),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildIllustrationSection() {
    return Center(
      child: CustomImageView(imagePath: ImageConstant.headerImgIllustration, height: 118.h, width: 152.h, fit: BoxFit.contain),
    );
  }

  Widget _buildHeaderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text('Login to you account', style: TextStyleHelper.instance.headline24SemiBold.copyWith(height: 1.25)),
        SizedBox(height: 8.h),
        Text('Welcome back! Please enter your details.', style: TextStyleHelper.instance.body14Regular.copyWith(height: 1.21)),
      ],
    );
  }

  Widget _buildLoginForm(BuildContext context, AuthenticationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildEmailField(context, state),
        SizedBox(height: 24.h),
        _buildPasswordField(context, state),
        SizedBox(height: 24.h),
        _buildForgotPasswordSection(context),
        SizedBox(height: 24.h),
        _buildLoginButton(context, state),
      ],
    );
  }

  Widget _buildEmailField(BuildContext context, AuthenticationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Email', style: TextStyleHelper.instance.body12Medium.copyWith(height: 1.25)),
        SizedBox(height: 8.h),
        CustomInputField(
          controller: state.emailController,
          prefixIcon: CustomImageView(imagePath: ImageConstant.email, height: 16.h, width: 16.h, fit: BoxFit.contain),
          hintText: 'Enter your email',
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.emailAddress,
          onChanged: (value) {
            context.read<AuthenticationBloc>().add(EmailChangedEvent(value));
          },
        ),
        // Display email validation error
        if (state.emailError != null) ...[
          SizedBox(height: 4.h),
          Text(state.emailError!, style: TextStyleHelper.instance.body12Regular.copyWith(color: appTheme.redCustom, height: 1.25)),
        ],
      ],
    );
  }

  Widget _buildPasswordField(BuildContext context, AuthenticationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Password', style: TextStyleHelper.instance.body12Medium.copyWith(height: 1.25)),
        SizedBox(height: 8.h),
        CustomInputField(
          controller: state.passwordController,
          hintText: 'Enter password',
          textInputAction: TextInputAction.done,
          obscureText: !(state.isPasswordVisible ?? false),
          onChanged: (value) {
            context.read<AuthenticationBloc>().add(PasswordChangedEvent(value));
          },
        ),
        // Display password validation error
        if (state.passwordError != null) ...[
          SizedBox(height: 4.h),
          Text(state.passwordError!, style: TextStyleHelper.instance.body12Regular.copyWith(color: appTheme.redCustom, height: 1.25)),
        ],
      ],
    );
  }

  Widget _buildForgotPasswordSection(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: () {
          context.read<AuthenticationBloc>().add(ForgotPasswordTappedEvent());
        },
        child: Text('Forgot password?', style: TextStyleHelper.instance.body12SemiBold.copyWith(height: 1.25)),
      ),
    );
  }

  Widget _buildLoginButton(BuildContext context, AuthenticationState state) {
    final isLoading = state.isLoading ?? false;
    final buttonPhase = state.buttonAnimationPhase ?? ButtonAnimationPhase.rectangular;

    // Determine button dimensions and border radius based on animation phase
    double buttonWidth;
    double borderRadius;

    switch (buttonPhase) {
      case ButtonAnimationPhase.rectangular:
        buttonWidth = 400.h; // Use a large finite width instead of infinity
        borderRadius = 8.h;
        break;
      case ButtonAnimationPhase.transitioningToCircular:
      case ButtonAnimationPhase.circular:
      case ButtonAnimationPhase.transitioningToRectangular:
        buttonWidth = 44.h; // Make it square/circular
        borderRadius = 22.h; // Half of height for perfect circle
        break;
    }

    return Center(
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: buttonWidth,
        height: 44.h,
        child: ElevatedButton(
          onPressed: isLoading
              ? null
              : () {
                  context.read<AuthenticationBloc>().add(LoginButtonTappedEvent());
                },
          style: ButtonStyle(
            // Ensure consistent background color in all states (enabled, disabled, pressed, etc.)
            backgroundColor: WidgetStateProperty.resolveWith<Color>((Set<WidgetState> states) {
              return appTheme.colorFF7E56; // Always use the same color regardless of state
            }),
            elevation: WidgetStateProperty.all<double>(1),
            shadowColor: WidgetStateProperty.all<Color>(appTheme.color0D1018),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                side: BorderSide(color: appTheme.colorFF7E56, width: 1),
              ),
            ),
            padding: WidgetStateProperty.all<EdgeInsetsGeometry>(
              EdgeInsets.symmetric(horizontal: buttonPhase == ButtonAnimationPhase.rectangular ? 16.h : 0),
            ),
            // Ensure text color remains consistent
            foregroundColor: WidgetStateProperty.all<Color>(appTheme.whiteCustom),
          ),
          child: isLoading
              ? SizedBox(
                  width: 20.h,
                  height: 20.h,
                  child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(appTheme.whiteCustom)),
                )
              : Text('Log in', style: TextStyleHelper.instance.title16Medium.copyWith(height: 1.25, color: appTheme.whiteCustom)),
        ),
      ),
    );
  }

  Widget _buildSocialLoginSection(BuildContext context) {
    return Column(
      children: [
        CustomButton(
          text: 'Log in with Google',
          onPressed: () {},
          variant: CustomButtonVariant.outlined,
          iconPath: ImageConstant.googleLogo,
          backgroundColor: appTheme.whiteCustom,
          borderColor: appTheme.colorFFCFD4,
          textColor: appTheme.colorFF1616,
          height: 44.h,
        ),
        SizedBox(height: 12.h),
        CustomButton(
          text: 'Log in with Apple',
          onPressed: () {},
          variant: CustomButtonVariant.outlined,
          iconPath: ImageConstant.appleLogo,
          backgroundColor: appTheme.whiteCustom,
          borderColor: appTheme.colorFFCFD4,
          textColor: appTheme.colorFF1616,
          height: 44.h,
        ),
      ],
    );
  }

  Widget _buildSignUpSection(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Don\'t have an account?', style: TextStyleHelper.instance.body12Regular.copyWith(height: 1.25)),
          SizedBox(width: 4.h),
          GestureDetector(
            onTap: () {
              context.read<AuthenticationBloc>().add(SignUpTappedEvent());
            },
            child: Text('Sign Up', style: TextStyleHelper.instance.body12SemiBold.copyWith(height: 1.25)),
          ),
        ],
      ),
    );
  }
}
