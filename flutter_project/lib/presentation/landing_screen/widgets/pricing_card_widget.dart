import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../core/utils/app_colors.dart';
import '../../../widgets/custom_button.dart';
import '../../../widgets/custom_image_view.dart';

class PricingCardWidget extends StatelessWidget {
  final String price;
  final String title;
  final String description;
  final List<String> features;
  final bool isPopular;
  final VoidCallback? onGetStarted;

  const PricingCardWidget({
    super.key,
    required this.price,
    required this.title,
    required this.description,
    required this.features,
    required this.isPopular,
    this.onGetStarted,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none, // allow overflow
      alignment: Alignment.topRight,
      children: [
        Container(
          width: double.maxFinite,
          padding: EdgeInsets.all(24.h),
          decoration: BoxDecoration(
            color: appTheme.whiteCustom,
            border: Border.all(color: appTheme.colorFFEAECF0, width: 1.h),
            borderRadius: BorderRadius.circular(16.h),
            boxShadow: [
              BoxShadow(color: appTheme.color0D1018, blurRadius: 6, offset: Offset(0, 4), spreadRadius: -2),
              BoxShadow(color: appTheme.color0D1018.withValues(alpha: 0.08), blurRadius: 16, offset: Offset(0, 12), spreadRadius: -4),
            ],
          ),
          child: Column(
            children: [
              SizedBox(height: isPopular ? 20.h : 0),
              Text(price, style: TextStyleHelper.instance.display36SemiBold.copyWith(color: appTheme.colorFF0F1728, height: 1.2)),
              SizedBox(height: 8.h),
              Text(title, style: TextStyleHelper.instance.headline20SemiBold.copyWith(color: appTheme.colorFF0F1728)),
              SizedBox(height: 4.h),
              Text(description, style: TextStyleHelper.instance.title16.copyWith(color: appTheme.colorFF667084)),
              SizedBox(height: 24.h),
              Column(
                children: features
                    .map(
                      (feature) => Padding(
                        padding: EdgeInsets.only(bottom: 16.h),
                        child: Row(
                          children: [
                            Container(
                              height: 24.h,
                              width: 24.h,
                              decoration: BoxDecoration(color: appTheme.colorFFD0FADF, borderRadius: BorderRadius.circular(12.h)),
                              child: Center(
                                child: CustomImageView(imagePath: ImageConstant.imgCheckIcon, height: 16.h, width: 16.h),
                              ),
                            ),
                            SizedBox(width: 12.h),
                            Expanded(
                              child: Text(feature, style: TextStyleHelper.instance.title16.copyWith(color: appTheme.colorFF667084)),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
              ),
              SizedBox(height: 24.h),
              CustomButton(text: 'Get started', backgroundColor: appTheme.colorFF7E56, onPressed: onGetStarted),
            ],
          ),
        ),
        if (isPopular)
          Positioned(
            top: -32.h,
            left: 0,
            right: 0,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: CustomImageView(imagePath: ImageConstant.imgVectors, height: 56.h, width: 56.h),
                ),
                SizedBox(width: 8.h),
                Text('Most popular!', style: TextStyleHelper.instance.body14Medium.copyWith(color: AppColors.primaryPurple)),
              ],
            ),
          ),
      ],
    );
  }
}
